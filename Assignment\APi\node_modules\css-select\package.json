{"name": "css-select", "version": "5.2.1", "description": "a CSS selector compiler/engine", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/fb55"}, "keywords": ["css", "selector", "sizzle"], "repository": {"type": "git", "url": "git://github.com/fb55/css-select.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["dist", "src"], "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.3", "domutils": "^3.2.2", "nth-check": "^2.1.1"}, "devDependencies": {"@types/boolbase": "^1.0.3", "@types/jest": "^29.5.14", "@types/node": "^24.0.4", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "htmlparser2": "^10.0.0", "jest": "^29.7.0", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/css-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4, "proseWrap": "always"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1.js", "$1"]}, "testMatch": ["<rootDir>/test/*.ts", "<rootDir>/**/*.spec.ts"]}}