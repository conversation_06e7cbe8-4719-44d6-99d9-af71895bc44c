{"name": "startech-scraper", "version": "1.0.0", "description": "Standalone backend to scrape Startech product and menu data", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["scraper", "startech", "api", "express"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cheerio": "1.0.0-rc.12", "dotenv": "^16.4.5", "express": "^4.21.0", "fs-extra": "^11.2.0", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.1.4"}}