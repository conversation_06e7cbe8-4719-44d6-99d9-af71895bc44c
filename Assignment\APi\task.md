Since you want a separate backend to scrape product and menu data from `https://www.startech.com.bd/gadget/smart-watch`, generate a JSON file, and save it locally for testing your app, I’ll provide a standalone Node.js backend solution. This backend will be independent of Next.js, organized in a separate folder structure, and will scrape the provided HTML for products and menu, save the data to a local JSON file, and optionally serve it via an Express API for your app to consume. The solution will use Cheerio for parsing HTML, fetch for HTTP requests, and save the JSON to a local file for testing.

### Project Structure

Here’s the folder structure for the standalone backend:

```
startech-scraper/
├── src/
│   ├── scrapers/
│   │   ├── productScraper.js    # Scrapes product data
│   │   └── menuScraper.js       # Scrapes menu data
│   ├── utils/
│   │   └── fileUtils.js         # Saves JSON to file
│   ├── routes/
│   │   └── api.js               # Express API routes
│   ├── data/
│   │   └── smart-watches.json   # Generated JSON file
│   └── server.js                # Main Express server
├── .env                         # Environment variables
├── package.json
└── README.md
```

### Step-by-Step Backend Code

#### 1. Initialize the Project

Create a new directory (`startech-scraper`) and initialize a Node.js project:

```bash
mkdir startech-scraper
cd startech-scraper
npm init -y
```

Install dependencies:

```bash
npm install express cheerio node-fetch fs-extra dotenv
```

- `express`: For creating an API server.
- `cheerio`: For parsing HTML.
- `node-fetch`: For HTTP requests.
- `fs-extra`: For file operations.
- `dotenv`: For environment variables.

#### 2. Environment Variables

Create a `.env` file in the root directory:

```env
STARTECH_URL=https://www.startech.com.bd/gadget/smart-watch
PORT=3001
```

#### 3. Scraper Modules

**`src/scrapers/productScraper.js`**
This module scrapes product data from the provided HTML structure (`.p-item` elements).

```javascript
const cheerio = require("cheerio");
const fetch = require("node-fetch");

async function scrapeProducts(url) {
  try {
    const response = await fetch(url, {
      headers: {
        Accept: "text/html",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    const $ = cheerio.load(html);

    const products = [];
    $(".p-item").each((i, element) => {
      const name = $(element).find(".p-item-name a").text().trim() || "N/A";
      const url = $(element).find(".p-item-name a").attr("href") || "";
      const image = $(element).find(".p-item-img img").attr("src") || "";
      const priceNew = $(element).find(".price-new").text().trim() || "N/A";
      const priceOld = $(element).find(".price-old").text().trim() || "N/A";
      const savings = $(element).find(".marks .mark").text().trim() || "N/A";
      const descriptionItems = $(element).find(".short-description ul li");
      const description = descriptionItems
        .map((_, li) => $(li).text().trim())
        .get()
        .join("; ");

      products.push({
        id: `product-${i + 1}`,
        name,
        price: priceNew,
        offerPrice: priceOld,
        savings,
        description,
        image: image.startsWith("http")
          ? image
          : `https://www.startech.com.bd${image}`,
        url: url.startsWith("http") ? url : `https://www.startech.com.bd${url}`,
      });
    });

    return products;
  } catch (error) {
    console.error("Error scraping products:", error);
    return [];
  }
}

module.exports = { scrapeProducts };
```

**`src/scrapers/menuScraper.js`**
This module scrapes the menu structure from the provided navigation HTML or a fetched page.

```javascript
const cheerio = require("cheerio");
const fetch = require("node-fetch");

async function scrapeMenu(url) {
  try {
    // Fetch the main page to get the full menu (or use provided HTML snippet)
    const response = await fetch(url, {
      headers: {
        Accept: "text/html",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    const $ = cheerio.load(html);

    const menu = [];
    $(".navbar-nav > .nav-item.has-child").each((i, element) => {
      const topLevelItem = {
        name: $(element).find("> .nav-link").text().trim(),
        url: $(element).find("> .nav-link").attr("href") || "",
        children: [],
      };

      $(element)
        .find(".drop-down.drop-menu-1 > .nav-item")
        .each((j, subElement) => {
          const subItem = {
            name: $(subElement).find("> .nav-link").text().trim(),
            url: $(subElement).find("> .nav-link").attr("href") || "",
            children: [],
          };

          if ($(subElement).hasClass("has-child")) {
            $(subElement)
              .find(".drop-down.drop-menu-2 > .nav-item")
              .each((k, subSubElement) => {
                subItem.children.push({
                  name: $(subSubElement).find(".nav-link").text().trim(),
                  url: $(subSubElement).find(".nav-link").attr("href") || "",
                });
              });
          }

          topLevelItem.children.push(subItem);
        });

      menu.push(topLevelItem);
    });

    return menu;
  } catch (error) {
    console.error("Error scraping menu:", error);
    return [];
  }
}

module.exports = { scrapeMenu };
```

**Notes**:

- The menu scraper fetches the page dynamically to ensure the full menu is captured. If you prefer to use the provided HTML snippet, you can modify the function to accept HTML directly (as shown in the previous response).
- The selectors (`.navbar-nav`, `.nav-item.has-child`, `.drop-menu-1`, `.drop-menu-2`) match the provided HTML structure.

#### 4. File Utility

**`src/utils/fileUtils.js`**
This module saves the JSON data to a local file.

```javascript
const fs = require("fs-extra");
const path = require("path");

async function saveToFile(data, filename) {
  try {
    const filePath = path.join(__dirname, "..", "data", filename);
    await fs.ensureDir(path.dirname(filePath));
    await fs.writeJson(filePath, data, { spaces: 2 });
    console.log(`Data saved to ${filePath}`);
  } catch (error) {
    console.error("Error saving to file:", error);
  }
}

module.exports = { saveToFile };
```

#### 5. Express API Routes

**`src/routes/api.js`**
This module defines API endpoints to scrape data and serve the JSON file.

```javascript
const express = require("express");
const router = express.Router();
const { scrapeProducts } = require("../scrapers/productScraper");
const { scrapeMenu } = require("../scrapers/menuScraper");
const { saveToFile } = require("../utils/fileUtils");
const fs = require("fs-extra");
const path = require("path");

// Scrape and save data
router.get("/scrape", async (req, res) => {
  try {
    const products = await scrapeProducts(process.env.STARTECH_URL);
    const menu = await scrapeMenu(process.env.STARTECH_URL);

    const data = {
      products,
      menu,
      totalProducts: products.length,
      timestamp: new Date().toISOString(),
    };

    await saveToFile(data, "smart-watches.json");
    res.status(200).json(data);
  } catch (error) {
    console.error("Error in /scrape:", error);
    res.status(500).json({ error: "Failed to scrape data" });
  }
});

// Serve saved JSON file
router.get("/data", async (req, res) => {
  try {
    const filePath = path.join(__dirname, "..", "data", "smart-watches.json");
    const data = await fs.readJson(filePath);
    res.status(200).json(data);
  } catch (error) {
    console.error("Error reading JSON file:", error);
    res.status(500).json({ error: "Failed to read data" });
  }
});

module.exports = router;
```

#### 6. Main Server

**`src/server.js`**
This sets up the Express server to run the API.

```javascript
const express = require("express");
const dotenv = require("dotenv");
const apiRoutes = require("./routes/api");

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(express.json());

// Routes
app.use("/api", apiRoutes);

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
```

#### 7. Package.json

Update `package.json` with scripts to run the server:

```json
{
  "name": "startech-scraper",
  "version": "1.0.0",
  "description": "Standalone backend to scrape Startech product and menu data",
  "main": "src/server.js",
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js"
  },
  "dependencies": {
    "cheerio": "^1.0.0",
    "dotenv": "^16.4.5",
    "express": "^4.21.0",
    "fs-extra": "^11.2.0",
    "node-fetch": "^2.7.0"
  },
  "devDependencies": {
    "nodemon": "^3.1.4"
  }
}
```

Install `nodemon` for development:

```bash
npm install --save-dev nodemon
```

#### 8. Sample JSON Output

The generated `src/data/smart-watches.json` will look like this (based on the provided HTML):

```json
{
  "products": [
    {
      "id": "product-1",
      "name": "COLMI P76 Smart Watch",
      "price": "1,790৳",
      "offerPrice": "1,890৳",
      "savings": "Save: 100৳",
      "description": "1.96 inch Extra Large Display; 100+ Sports Modes; Super long battery life; Waterproof level: 3ATM",
      "image": "https://www.startech.com.bd/image/cache/catalog/smart-watch/colmi/p76/p76-black-orange-official-228x228.webp",
      "url": "https://www.startech.com.bd/colmi-p76-bluetooth-calling-smart-watch"
    },
    {
      "id": "product-2",
      "name": "Oraimo Watch 5 Lite Smart Watch",
      "price": "1,750৳",
      "offerPrice": "1,899৳",
      "savings": "Save: 149৳",
      "description": "2.01\" TFT HD 240x280p Display, HD Voice in Calls; AI-Generated Watch Faces; IP68 Dust & Water Resistance; 105+ Sports Modes, 7 Days Battery Life",
      "image": "https://www.startech.com.bd/image/cache/catalog/smart-watch/oraimo/watch-5-lite/watch-5-lite-official-228x228.webp",
      "url": "https://www.startech.com.bd/oraimo-watch-5-lite-smart-watch"
    },
    {
      "id": "product-3",
      "name": "COLMI P73 Smart Watch",
      "price": "1,840৳",
      "offerPrice": "1,990৳",
      "savings": "Save: 150৳",
      "description": "1.90-inch High-Definition Screen; 100+ Sports Modes; 300mAh Battery Capacity; Waterproof level: IP68 Waterproof",
      "image": "https://www.startech.com.bd/image/cache/catalog/smart-watch/colmi/p73/p73-orange-black-camouflage-official-228x228.webp",
      "url": "https://www.startech.com.bd/colmi-p73-bluetooth-calling-smart-watch"
    },
    {
      "id": "product-4",
      "name": "Riversong Motive 6 Pro Smart Watch",
      "price": "1,850৳",
      "offerPrice": "1,999৳",
      "savings": "Save: 149৳",
      "description": "Screen: 1.83 Full Touch Display; Battery Capacity: 200mAh; Water Resistance: IP68; Features: Female Health Tracker, Bluetooth Calling",
      "image": "https://www.startech.com.bd/image/cache/catalog/smartwatch/riversong/motive-6-pro/motive-6-pro1-228x228.webp",
      "url": "https://www.startech.com.bd/riversong-motive-6-pro-smart-watch"
    },
    {
      "id": "product-5",
      "name": "Riversong Motive 3C SW31 Smart Watch",
      "price": "1,910৳",
      "offerPrice": "2,099৳",
      "savings": "Save: 189৳",
      "description": "Display: 1.28\" HD Colorful Screen; Bluetooth: BT 5.1; Battery Life: Up to 7 days; Waterproof Grade: IP68",
      "image": "https://www.startech.com.bd/image/cache/catalog/smartwatch/riversong/motive-3c-sw31/motive-3c-sw31-01-228x228.webp",
      "url": "https://www.startech.com.bd/riversong-motive-3c-sw31-smart-watch"
    },
    {
      "id": "product-6",
      "name": "XTRA Active S5 Pro+ Smart Watch",
      "price": "1,950৳",
      "offerPrice": "2,099৳",
      "savings": "Save: 149৳",
      "description": "Display: 1.83’’ Display, 240*284 Resolution; Battery: Li-polymer 230mAh; Heart rate monitoring, Blood Pressure; Sleep monitoring, IP67 Waterproof, 100+ Sports Mode",
      "image": "https://www.startech.com.bd/image/cache/catalog/smart-watch/xtra/active-s5-pro-plus/active-s5-pro-plus-01-228x228.webp",
      "url": "https://www.startech.com.bd/xtra-active-s5-pro-plus-smart-watch"
    },
    {
      "id": "product-7",
      "name": "Havit M9035 Smart Watch",
      "price": "1,990৳",
      "offerPrice": "2,150৳",
      "savings": "Save: 160৳",
      "description": "1.83\" TFT Full Touch Screen; Battery capacity: 240mAh; IP68 Waterproof Rating; 100+ Dials Match Your Life Occasion",
      "image": "https://www.startech.com.bd/image/cache/catalog/smart-watch/havit/m9035/m9035-black-01-228x228.webp",
      "url": "https://www.startech.com.bd/havit-m9035-smart-watch"
    }
  ],
  "menu": [
    {
      "name": "Desktop",
      "url": "https://www.startech.com.bd/desktops",
      "children": [
        {
          "name": "Desktop Offer",
          "url": "https://www.startech.com.bd/special-pc",
          "children": []
        },
        {
          "name": "Star PC",
          "url": "https://www.startech.com.bd/desktops/star-pc",
          "children": [
            {
              "name": "Intel PC",
              "url": "https://www.startech.com.bd/intel-pc"
            },
            {
              "name": "Ryzen PC",
              "url": "https://www.startech.com.bd/ryzen-pc"
            }
          ]
        },
        {
          "name": "Gaming PC",
          "url": "https://www.startech.com.bd/desktops/gaming-pc",
          "children": [
            {
              "name": "Intel PC",
              "url": "https://www.startech.com.bd/desktops/gaming-pc/intel-gaming-pc"
            },
            {
              "name": "RYZEN PC",
              "url": "https://www.startech.com.bd/desktops/gaming-pc/amd-gaming-pc"
            }
          ]
        },
        {
          "name": "Brand PC",
          "url": "https://www.startech.com.bd/desktops/brand-pc",
          "children": [
            {
              "name": "Acer",
              "url": "https://www.startech.com.bd/desktops/brand-pc/acer-desktop"
            },
            {
              "name": "ASUS",
              "url": "https://www.startech.com.bd/desktops/brand-pc/asus-desktop"
            },
            {
              "name": "Dell",
              "url": "https://www.startech.com.bd/desktops/brand-pc/dell-desktop"
            },
            {
              "name": "HP",
              "url": "https://www.startech.com.bd/desktops/brand-pc/hp-desktop"
            },
            {
              "name": "Lenovo",
              "url": "https://www.startech.com.bd/desktops/brand-pc/lenovo-desktop"
            },
            {
              "name": "MSI",
              "url": "https://www.startech.com.bd/msi-brand-pc"
            }
          ]
        },
        {
          "name": "All-in-One PC",
          "url": "https://www.startech.com.bd/desktops/all-in-one-pc",
          "children": [
            {
              "name": "Dell",
              "url": "https://www.startech.com.bd/dell-all-in-one-pc"
            },
            {
              "name": "HP",
              "url": "https://www.startech.com.bd/hp-all-in-one-pc"
            },
            {
              "name": "ASUS",
              "url": "https://www.startech.com.bd/asus-all-in-one-pc"
            },
            {
              "name": "LENOVO",
              "url": "https://www.startech.com.bd/desktops/all-in-one-pc/lenovo-all-in-one"
            },
            {
              "name": "Teclast",
              "url": "https://www.startech.com.bd/teclast-all-in-one-pc"
            },
            {
              "name": "AOC",
              "url": "https://www.startech.com.bd/aoc-all-in-one-pc"
            },
            {
              "name": "Smart",
              "url": "https://www.startech.com.bd/smart-all-in-one-pc"
            }
          ]
        },
        {
          "name": "Portable Mini PC",
          "url": "https://www.startech.com.bd/desktops/portable-mini-pc",
          "children": [
            {
              "name": "Asus",
              "url": "https://www.startech.com.bd/asus-mini-pc"
            },
            {
              "name": "Zotac",
              "url": "https://www.startech.com.bd/zotac-portable-mini-pc"
            }
          ]
        }
      ]
    }
  ],
  "totalProducts": 7,
  "timestamp": "2025-06-28T18:22:00.000Z"
}
```

#### 9. Running the Backend

1. Start the server:

   ```bash
   npm run dev
   ```

   This uses `nodemon` for auto-reloading during development.

2. Scrape and save data:

   - Visit `http://localhost:3001/api/scrape` in your browser or use `curl`:
     ```bash
     curl http://localhost:3001/api/scrape
     ```
   - This fetches data, saves it to `src/data/smart-watches.json`, and returns the JSON.

3. Access saved data:
   - Visit `http://localhost:3001/api/data` or:
     ```bash
     curl http://localhost:3001/api/data
     ```
   - This serves the saved `smart-watches.json` without re-scraping, ideal for testing.

#### 10. Testing with Your App

- **Fetch Data**: Your front-end (React, Next.js, or other) can fetch data from `http://localhost:3001/api/data`:
  ```javascript
  fetch("http://localhost:3001/api/data")
    .then((res) => res.json())
    .then((data) => console.log(data.products, data.menu));
  ```
- **Local JSON File**: Use the `src/data/smart-watches.json` file directly in your app for testing:
  ```javascript
  import data from "./smart-watches.json"; // Adjust path based on your app
  console.log(data.products, data.menu);
  ```

#### 11. Legal and Ethical Considerations

- **Scraping**: Scraping `https://www.startech.com.bd` may violate their terms of service. Check their Terms of Use or contact support for permission or API access.
- **Testing Only**: Since you’re using this for testing, avoid using scraped data in production without permission.
- **Rate Limiting**: Limit requests (e.g., one per minute) to avoid overloading Startech’s server. Add a delay if scraping frequently:
  ```javascript
  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
  await sleep(1000); // 1-second delay
  ```

#### 12. Enhancements

- **Dynamic Menu Fetching**: The menu scraper fetches the page dynamically. If the menu HTML changes, verify selectors in DevTools.
- **Error Handling**: The code includes basic error handling. Add more specific checks (e.g., for missing selectors) if needed.
- **Pagination**: If `https://www.startech.com.bd/gadget/smart-watch` has multiple pages, check for pagination links (e.g., `<a class="next-page">`) and modify `productScraper.js`:
  ```javascript
  const nextPage = $(".pagination .next-page").attr("href");
  if (nextPage) {
    const nextProducts = await scrapeProducts(nextPage);
    products.push(...nextProducts);
  }
  ```
- **Cron Job for Updates**: Schedule periodic scraping with `node-cron`:
  ```bash
  npm install node-cron
  ```
  Add to `server.js`:
  ```javascript
  const cron = require("node-cron");
  cron.schedule("0 */5 * * * *", async () => {
    const products = await scrapeProducts(process.env.STARTECH_URL);
    const menu = await scrapeMenu(process.env.STARTECH_URL);
    await saveToFile(
      {
        products,
        menu,
        totalProducts: products.length,
        timestamp: new Date().toISOString(),
      },
      "smart-watches.json"
    );
  });
  ```

#### 13. Notes

- **Selectors**: The product selectors (`.p-item`, `.p-item-name`, `.price-new`, etc.) match the provided HTML. If the live page differs, inspect `https://www.startech.com.bd/gadget/smart-watch` in DevTools and update selectors.
- **Menu HTML**: The menu scraper uses the full page to ensure completeness. If you want to use the provided HTML snippet only, modify `menuScraper.js` to accept a string:
  ```javascript
  function scrapeMenu(html) {
    const $ = cheerio.load(html);
    // ... rest of the code
  }
  ```
- **Testing**: The `/api/data` endpoint allows you to test without re-scraping, reducing load on Startech’s server.
- **Deployment**: For local testing, run on `localhost`. For production, deploy to a server (e.g., Heroku, Render) and update your app’s fetch URL.

#### 14. Next Steps

- **Verify Selectors**: Test the scraper locally to ensure all products and menu items are captured correctly.
- **Integrate with Front-End**: Update your app to fetch from `http://localhost:3001/api/data` or read `smart-watches.json`.
- **Share HTML**: If more products or menu items are needed, share additional HTML snippets or specific requirements.
- **API Check**: If you find a Startech API (e.g., via DevTools), I can modify the code to use it instead of scraping.

Run the server (`npm run dev`), access `http://localhost:3001/api/scrape` to generate the JSON, and use `http://localhost:3001/api/data` or `src/data/smart-watches.json` in your app. Let me know if you need help integrating with your front-end or refining the JSON structure!
