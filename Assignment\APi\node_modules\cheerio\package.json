{"name": "cheerio", "version": "1.0.0-rc.12", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "author": "<PERSON> <matt<PERSON><PERSON>@gmail.com>", "maintainers": ["<PERSON> <<EMAIL>>"], "funding": "https://github.com/cheeriojs/cheerio?sponsor=1", "license": "MIT", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio.git"}, "bugs": {"url": "https://github.com/cheeriojs/cheerio/issues"}, "homepage": "https://cheerio.js.org/", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {".": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "./lib/slim": {"require": "./lib/slim.js", "import": "./lib/esm/slim.js"}, "./lib/utils": {"require": "./lib/utils.js", "import": "./lib/esm/utils.js"}}, "files": ["lib"], "engines": {"node": ">= 6"}, "dependencies": {"cheerio-select": "^2.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "domutils": "^3.0.1", "htmlparser2": "^8.0.1", "parse5": "^7.0.0", "parse5-htmlparser2-tree-adapter": "^7.0.0"}, "devDependencies": {"@octokit/graphql": "^4.8.0", "@types/benchmark": "^2.1.1", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "benchmark": "^2.1.4", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-jsdoc": "^39.3.3", "eslint-plugin-node": "^11.1.0", "husky": "^8.0.1", "jest": "^28.1.1", "jquery": "^3.6.0", "jsdom": "^20.0.0", "lint-staged": "^13.0.2", "prettier": "^2.7.1", "prettier-plugin-jsdoc": "0.3.38", "ts-jest": "^28.0.5", "ts-node": "^10.8.1", "typedoc": "^0.22.17", "typescript": "^4.7.4", "undici": "^5.5.1"}, "scripts": {"test": "npm run lint && npm run test:jest", "test:jest": "jest", "test:jest:cov": "npm run test:jest -- --coverage", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier \"**/*.{{m,c,}js,ts,md,json,yml}\" --ignore-path .gitignore", "build:docs": "typedoc --hideGenerator src/index.ts", "benchmark": "npm run build:cjs && ts-node benchmark/benchmark.ts --regex \"^(?!.*highmem)\"", "update-sponsors": "ts-node scripts/fetch-sponsors.ts", "bench": "npm run benchmark", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepublishOnly": "npm run build", "prepare": "husky install"}, "prettier": {"singleQuote": true, "tabWidth": 2, "tsdoc": true}, "lint-staged": {"*.js": ["prettier --write", "npm run test:lint -- --fix"], "*.{json,md,ts,yml}": ["prettier --write"]}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/__fixtures__/"], "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1.js", "$1"]}}}