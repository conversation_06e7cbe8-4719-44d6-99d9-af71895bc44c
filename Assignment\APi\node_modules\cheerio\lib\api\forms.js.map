{"version": 3, "file": "forms.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/forms.ts"], "names": [], "mappings": ";;;AAEA,wCAAoC;AAEpC;;;GAGG;AACH,IAAM,mBAAmB,GAAG,8BAA8B,CAAC;AAC3D,IAAM,GAAG,GAAG,MAAM,CAAC;AACnB,IAAM,KAAK,GAAG,QAAQ,CAAC;AAEvB;;;;;;;;;;;;;GAaG;AACH,SAAgB,SAAS;IACvB,gDAAgD;IAChD,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IAElC,iDAAiD;IACjD,IAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CACpB,UAAC,IAAI;QACH,OAAA,UAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAE;IAApE,CAAoE,CACvE,CAAC;IAEF,qCAAqC;IACrC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC5C,CAAC;AAZD,8BAYC;AAOD;;;;;;;;;;;;;GAaG;AACH,SAAgB,cAAc;IAA9B,iBAwCC;IArCC,8EAA8E;IAC9E,OAAO,IAAI,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,IAAI;QACtB,IAAM,KAAK,GAAG,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;YACvC,OAAO,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC;SAClD;QACD,OAAO,KAAK,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC;IACrD,CAAC,CAAC;SACC,MAAM;IACL,8EAA8E;IAC9E,oBAAoB;QAClB,iGAAiG;QACjG,+CAA+C;QAC/C,sDAAsD;QACtD,8CAA8C;IAChD,+CAA+C;KAChD;SACA,GAAG,CAA2B,UAAC,CAAC,EAAE,IAAI;;QACrC,IAAM,KAAK,GAAG,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAW,CAAC,CAAC,oDAAoD;QAC/F,mFAAmF;QACnF,IAAM,KAAK,GAAG,MAAA,KAAK,CAAC,GAAG,EAAE,mCAAI,EAAE,CAAC;QAEhC,+FAA+F;QAC/F,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,GAAG;gBACnB;;;mBAGG;gBACH,OAAA,CAAC,EAAE,IAAI,MAAA,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;YAA7C,CAA6C,CAC9C,CAAC;SACH;QACD,wEAAwE;QACxE,OAAO,EAAE,IAAI,MAAA,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC;IACvD,CAAC,CAAC;SACD,OAAO,EAAE,CAAC;AACf,CAAC;AAxCD,wCAwCC"}