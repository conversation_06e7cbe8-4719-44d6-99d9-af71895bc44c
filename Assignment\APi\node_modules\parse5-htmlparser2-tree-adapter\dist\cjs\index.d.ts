import { type TreeAdapterTypeMap, type TreeAdapter } from 'parse5';
import { type AnyNode, type ParentNode, type ChildNode, Element, Document, ProcessingInstruction, Comment, Text } from 'domhandler';
export type Htmlparser2TreeAdapterMap = TreeAdapterTypeMap<AnyNode, ParentNode, ChildNode, Document, Document, Element, Comment, Text, Element, ProcessingInstruction>;
/** @internal */
export declare function serializeDoctypeContent(name: string, publicId: string, systemId: string): string;
export declare const adapter: TreeAdapter<Htmlparser2TreeAdapterMap>;
